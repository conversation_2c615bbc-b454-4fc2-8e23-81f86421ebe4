import './bootstrap';

// Enhanced Slideshow and Counter Animation
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Featured Slideshow
    initializeFeaturedSlideshow();

    // Initialize AOS-like animations
    initializeAnimations();
    const startCounters = () => {
        const counters = document.querySelectorAll('.counter-value');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const startTime = performance.now();
            const startValue = 0;

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOutQuad = progress * (2 - progress);
                const currentValue = Math.floor(startValue + (target - startValue) * easeOutQuad);

                counter.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        });
    };

    // Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                startCounters();
                observer.disconnect(); // Only run once
            }
        });
    }, { threshold: 0.1 });

    // Observe the stats section
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
        observer.observe(statsSection);
    }
});

// Featured Slideshow Functions
function initializeFeaturedSlideshow() {
    const carousel = document.getElementById('featuredCarousel');
    if (!carousel) return;

    // Enhanced carousel controls
    const prevBtn = carousel.querySelector('.carousel-control-prev');
    const nextBtn = carousel.querySelector('.carousel-control-next');
    const indicators = carousel.querySelectorAll('.indicator-btn');

    // Add smooth transitions
    carousel.addEventListener('slide.bs.carousel', function (e) {
        const activeItem = carousel.querySelector('.carousel-item.active');
        const nextItem = e.relatedTarget;

        // Add fade effect
        if (activeItem && nextItem) {
            activeItem.style.opacity = '0';
            setTimeout(() => {
                activeItem.style.opacity = '1';
            }, 400);
        }
    });

    // Enhanced indicator functionality
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', function() {
            indicators.forEach(ind => ind.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Pause on hover
    carousel.addEventListener('mouseenter', function() {
        if (typeof bootstrap !== 'undefined') {
            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
            if (carouselInstance) {
                carouselInstance.pause();
            }
        }
    });

    carousel.addEventListener('mouseleave', function() {
        if (typeof bootstrap !== 'undefined') {
            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
            if (carouselInstance) {
                carouselInstance.cycle();
            }
        }
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let endX = 0;

    carousel.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
    });

    carousel.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        handleSwipe();
    });

    function handleSwipe() {
        const threshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > threshold) {
            if (typeof bootstrap !== 'undefined') {
                const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                if (carouselInstance) {
                    if (diff > 0) {
                        carouselInstance.next();
                    } else {
                        carouselInstance.prev();
                    }
                }
            }
        }
    }
}

// Simple AOS-like animations
function initializeAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');

    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const delay = element.getAttribute('data-aos-delay') || 0;

                setTimeout(() => {
                    element.classList.add('aos-animate');
                }, parseInt(delay));

                animationObserver.unobserve(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(element => {
        animationObserver.observe(element);
    });
}
