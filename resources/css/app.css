/* Import statements must come first */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Persian Font Setup */
body {
    font-family: Vazir, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}

/* Adjust line height for Persian text */
html[lang="fa"] {
    line-height: 1.8;
}

/* RTL dropdown menu alignment for Persian language */
html[dir="rtl"] .dropdown-menu {
    text-align: right;
    left: auto;
    right: 0;
}

html[dir="rtl"] .dropdown-item {
    text-align: right !important;
    direction: rtl !important;
    padding-right: 1rem !important;
    padding-left: 1rem !important;
    display: block;
    width: 100%;
}

/* Dropdown fixes */
.dropdown-menu {
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
    margin-left: 0;
}

/* Featured Slideshow Styles */
.featured-slideshow {
    min-height: 600px;
}

.featured-slideshow .carousel-item {
    transition: transform 0.8s ease-in-out;
}

.featured-slideshow .carousel-fade .carousel-item {
    opacity: 0;
    transition-property: opacity;
    transform: none;
}

.featured-slideshow .carousel-fade .carousel-item.active,
.featured-slideshow .carousel-fade .carousel-item-next.carousel-item-start,
.featured-slideshow .carousel-fade .carousel-item-prev.carousel-item-end {
    z-index: 1;
    opacity: 1;
}

.featured-slideshow .carousel-fade .active.carousel-item-start,
.featured-slideshow .carousel-fade .active.carousel-item-end {
    z-index: 0;
    opacity: 0;
    transition: opacity 0s 0.8s;
}

/* Custom Carousel Indicators */
.featured-slideshow .carousel-indicators .indicator-btn.active {
    background: rgba(255,255,255,0.9) !important;
    border-color: rgba(255,255,255,0.9) !important;
    transform: scale(1.2);
}

.featured-slideshow .carousel-indicators .indicator-btn:hover {
    background: rgba(255,255,255,0.7) !important;
    border-color: rgba(255,255,255,0.7) !important;
    transform: scale(1.1);
}

/* Image hover effects */
.featured-slideshow .image-container img:hover {
    transform: scale(1.05);
}

/* Custom navigation buttons */
.featured-slideshow .carousel-control-prev:hover,
.featured-slideshow .carousel-control-next:hover {
    background: rgba(255,255,255,1) !important;
    transform: translateY(-50%) scale(1.1) !important;
}

/* Hover lift effect */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

/* Background gradient utilities */
.bg-gradient-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #63D1CC 100%) !important;
}

/* Text utilities */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

/* Object fit utility */
.object-fit-cover {
    object-fit: cover !important;
}

/* Min height utilities */
.min-vh-50 {
    min-height: 50vh !important;
}

/* Animation delays for AOS */
[data-aos="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

[data-aos="fade-up"].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* RTL support for slideshow */
html[dir="rtl"] .featured-slideshow .carousel-control-prev {
    right: -25px;
    left: auto;
}

html[dir="rtl"] .featured-slideshow .carousel-control-next {
    left: -25px;
    right: auto;
}

html[dir="rtl"] .featured-slideshow .carousel-control-prev i:before {
    content: "\f054"; /* chevron-right */
}

html[dir="rtl"] .featured-slideshow .carousel-control-next i:before {
    content: "\f053"; /* chevron-left */
}

/* Fix spacing for nav items in RTL mode */
html[dir="rtl"] .nav-item.ms-3 {
    margin-left: 0 !important;
    margin-right: 1rem !important;
}

/* Fix dropdown menu item alignment in RTL mode */
html[dir="rtl"] .dropdown-menu .dropdown-item::before {
    float: right;
    margin-left: 0.5rem;
    margin-right: -0.5rem;
}

/* Ensure dropdown toggle caret is properly positioned in RTL */
html[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

/* Font weights */
h1, h2, h3, h4, h5, h6 {
    font-family: Vazir;
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

.font-light {
    font-weight: 300;
}

/* Your custom styles here */
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.bg-turquoise {
    background-color: #40E0D0;
}

.text-turquoise {
    color: #40E0D0;
}

/* Add hover effect */
.service-card:hover .service-icon {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}
