@extends('layouts.app')

@section('title', __('messages.home'))

@section('meta_description', __('messages.home_meta_description'))

@section('meta_keywords', __('messages.home_meta_keywords'))

@section('content')
    <!-- Hero Section -->
    <header class="hero-section position-relative overflow-hidden pb-5">
        <!-- Animated Background -->
        <div class="position-absolute w-100 h-100" style="background: linear-gradient(135deg, #005B7F 0%, #63C5E0 100%); z-index: -2;"></div>
        <div class="position-absolute w-100 h-100" style="background: url('{{ asset('images/pattern-grid.png') }}') repeat; opacity: 0.1; z-index: -1;"></div>

        <div class="container py-7">
            <div class="row align-items-center min-vh-85">
                <!-- Content Column -->
                <div class="col-lg-6 pe-lg-5">
                    <div data-animate="fade-in-up">
                        <!-- Welcome Badge -->
                        <div class="mb-4" data-animate="fade-in-up delay-1">
                            <span class="badge bg-white text-primary px-4 py-2 rounded-pill shadow-sm">
                                <i class="fas fa-star-of-life me-2"></i>{{ __('messages.welcome_to') }}
                            </span>
                        </div>

                        <!-- Heading -->
                        <h1 class="display-3 fw-bold text-white mb-4" data-animate="fade-in-up delay-2">
                            {{ __('messages.expert_corporate') }}
                            <span class="position-relative">
                                {{ __('messages.accounting') }}
                            </span>
                            {{ __('messages.solutions') }}
                        </h1>

                        <!-- Description -->
                        <p class="lead text-white-75 mb-5" data-animate="fade-in-up delay-3"
                           style="font-size: 1.4rem;">
                            {{ __('messages.empowering_businesses') }}
                        </p>

                        <!-- Buttons -->
                        <div class="d-flex gap-4" data-animate="fade-in-up delay-3">
                            <a href="#contact" class="btn btn-light btn-lg px-4 rounded-pill hover-lift">
                                <i class="fas fa-rocket me-2"></i>{{ __('messages.get_started') }}
                            </a>
                            <a href="{{ route('services') }}"
                               class="btn btn-outline-light btn-lg px-4 rounded-pill hover-lift">
                                <i class="fas fa-arrow-right me-2"></i>{{ __('messages.our_services') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Image Column -->
                <div class="col-lg-6 d-none d-lg-block" data-animate="slide-in-right">
                    <div class="position-relative">
                        <img src="{{ asset('images/hero/accounting-office.jpg') }}"
                             alt="{{ __('messages.accounting_office_alt') }}"
                             class="img-fluid rounded-4 shadow-lg hero-image"
                             width="600"
                             height="400">
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Shapes -->
        <div class="position-absolute top-50 start-0 translate-middle-y">
            <div class="d-flex gap-4">
                <div class="shape-dot"></div>
                <div class="shape-dot"></div>
            </div>
        </div>
        <div class="position-absolute bottom-0 end-0">
            <div class="shape-wave"></div>
        </div>
    </header>

    <!-- Featured Articles & News Slideshow -->
    @php
        $featuredArticles = collect();
        $featuredNews = collect();

        // Check if models exist before querying
        if (class_exists('\App\Models\Article')) {
            try {
                $featuredArticles = \App\Models\Article::where('is_featured', true)
                    ->where('is_published', true)
                    ->orderBy('published_at', 'desc')
                    ->limit(3)
                    ->get();
            } catch (\Exception $e) {
                $featuredArticles = collect();
            }
        }

        if (class_exists('\App\Models\News')) {
            try {
                $featuredNews = \App\Models\News::where('is_featured', true)
                    ->where('is_published', true)
                    ->orderBy('published_at', 'desc')
                    ->limit(3)
                    ->get();
            } catch (\Exception $e) {
                $featuredNews = collect();
            }
        }

        $featuredItems = $featuredArticles->merge($featuredNews)->sortByDesc('created_at')->take(6);
    @endphp

    @if($featuredItems->count() > 0)
    <section class="featured-slideshow py-5 position-relative overflow-hidden">
        <!-- Enhanced Background -->
        <div class="position-absolute w-100 h-100 top-0 start-0"
             style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #63D1CC 100%); z-index: -3;"></div>
        <div class="position-absolute w-100 h-100 top-0 start-0"
             style="background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>') repeat; z-index: -2;"></div>

        <div class="container">
            <!-- Section Header -->
            <div class="text-center mb-5" data-aos="fade-up">
                <span class="badge bg-white bg-opacity-20 text-white px-4 py-2 mb-3 rounded-pill">
                    <i class="fas fa-star me-2"></i>{{ app()->getLocale() == 'fa' ? 'مطالب ویژه' : 'Featured Content' }}
                </span>
                <h2 class="display-5 fw-bold text-white mb-3">
                    {{ app()->getLocale() == 'fa' ? 'آخرین مقالات و اخبار ویژه' : 'Latest Featured Articles & News' }}
                </h2>
                <p class="lead text-white-75 mb-0">
                    {{ app()->getLocale() == 'fa' ? 'به‌روزترین اطلاعات و تحلیل‌های تخصصی از دنیای حسابداری و کسب‌وکار' : 'Latest insights and professional analysis from the world of accounting and business' }}
                </p>
            </div>

            <!-- Enhanced Carousel -->
            <div id="featuredCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="6000" data-aos="fade-up" data-aos-delay="200">
                <!-- Custom Indicators -->
                <div class="carousel-indicators position-relative mb-4" style="position: relative !important; margin-bottom: 2rem !important;">
                    @foreach($featuredItems->take(6) as $index => $item)
                        <button type="button" data-bs-target="#featuredCarousel" data-bs-slide-to="{{ $index }}"
                                class="indicator-btn {{ $index === 0 ? 'active' : '' }}"
                                aria-current="true" aria-label="Slide {{ $index + 1 }}"
                                style="width: 12px; height: 12px; border-radius: 50%; margin: 0 6px; border: 2px solid rgba(255,255,255,0.5); background: transparent; transition: all 0.3s ease;">
                        </button>
                    @endforeach
                </div>

                <div class="carousel-inner rounded-4 shadow-lg overflow-hidden">
                    @foreach($featuredItems->take(6) as $index => $item)
                        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                            <div class="row g-0 align-items-center min-vh-50">
                                <!-- Image Column -->
                                <div class="col-lg-6 position-relative">
                                    <div class="image-container position-relative overflow-hidden" style="height: 450px;">
                                        @if(isset($item->featured_image) && $item->featured_image)
                                            <img src="{{ asset('storage/' . $item->featured_image) }}"
                                                 class="img-fluid w-100 h-100 object-fit-cover"
                                                 alt="{{ $item->title }}"
                                                 style="transition: transform 0.5s ease;">
                                        @else
                                            <div class="bg-gradient-primary d-flex align-items-center justify-content-center h-100">
                                                <div class="text-center text-white">
                                                    <i class="fas fa-{{ $item instanceof \App\Models\Article ? 'file-alt' : 'newspaper' }} fa-4x mb-3 opacity-75"></i>
                                                    <h4>{{ $item instanceof \App\Models\Article ? (app()->getLocale() == 'fa' ? 'مقاله' : 'Article') : (app()->getLocale() == 'fa' ? 'خبر' : 'News') }}</h4>
                                                </div>
                                            </div>
                                        @endif
                                        <!-- Overlay -->
                                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25"></div>
                                    </div>
                                </div>

                                <!-- Content Column -->
                                <div class="col-lg-6">
                                    <div class="p-5 bg-white h-100 d-flex flex-column justify-content-center">
                                        <!-- Category Badge -->
                                        <div class="mb-3">
                                            <span class="badge bg-primary bg-gradient px-3 py-2 rounded-pill">
                                                <i class="fas fa-{{ $item instanceof \App\Models\Article ? 'file-alt' : 'newspaper' }} me-2"></i>
                                                {{ $item instanceof \App\Models\Article ? (app()->getLocale() == 'fa' ? 'مقاله ویژه' : 'Featured Article') : (app()->getLocale() == 'fa' ? 'خبر ویژه' : 'Featured News') }}
                                            </span>
                                        </div>

                                        <!-- Title -->
                                        <h3 class="h2 fw-bold text-dark mb-3 lh-base">
                                            {{ app()->getLocale() == 'fa' ? $item->title_fa : ($item->title_en ?? $item->title_fa) }}
                                        </h3>

                                        <!-- Excerpt -->
                                        <p class="text-muted mb-4 lh-lg">
                                            @php
                                                $summary = app()->getLocale() == 'fa' ? $item->summary_fa : ($item->summary_en ?? $item->summary_fa);
                                                $content = app()->getLocale() == 'fa' ? $item->content_fa : ($item->content_en ?? $item->content_fa);
                                                $excerpt = $summary ?? strip_tags($content);
                                            @endphp
                                            {{ Str::limit($excerpt, 180) }}
                                        </p>

                                        <!-- Meta Info -->
                                        <div class="d-flex align-items-center mb-4 text-muted small">
                                            <div class="me-4">
                                                <i class="fas fa-calendar me-2"></i>
                                                {{ ($item->published_at ?? $item->created_at)->format(app()->getLocale() == 'fa' ? 'Y/m/d' : 'M d, Y') }}
                                            </div>
                                            @if(isset($item->views_count))
                                                <div class="me-4">
                                                    <i class="fas fa-eye me-2"></i>
                                                    {{ number_format($item->views_count) }} {{ app()->getLocale() == 'fa' ? 'بازدید' : 'views' }}
                                                </div>
                                            @endif
                                            @if(isset($item->category) && $item->category)
                                                <div>
                                                    <i class="fas fa-tag me-2"></i>
                                                    {{ $item->category->name }}
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Read More Button -->
                                        <div>
                                            <a href="{{ $item instanceof \App\Models\Article ? route('articles.show', $item->slug) : route('news.show', $item->slug) }}"
                                               class="btn btn-primary btn-lg px-4 rounded-pill shadow-sm hover-lift">
                                                {{ app()->getLocale() == 'fa' ? 'ادامه مطلب' : 'Read More' }}
                                                <i class="fas fa-arrow-{{ app()->getLocale() == 'fa' ? 'left' : 'right' }} ms-2"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Custom Navigation Buttons -->
                <button class="carousel-control-prev" type="button" data-bs-target="#featuredCarousel" data-bs-slide="prev"
                        style="width: 50px; height: 50px; background: rgba(255,255,255,0.9); border-radius: 50%; top: 50%; transform: translateY(-50%); left: -25px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <i class="fas fa-chevron-left text-primary"></i>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#featuredCarousel" data-bs-slide="next"
                        style="width: 50px; height: 50px; background: rgba(255,255,255,0.9); border-radius: 50%; top: 50%; transform: translateY(-50%); right: -25px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <i class="fas fa-chevron-right text-primary"></i>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>

            <!-- View All Button -->
            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="400">
                <div class="row justify-content-center">
                    <div class="col-auto me-3">
                        <a href="{{ route('articles.index') }}" class="btn btn-outline-light btn-lg px-4 rounded-pill">
                            <i class="fas fa-file-alt me-2"></i>{{ app()->getLocale() == 'fa' ? 'همه مقالات' : 'All Articles' }}
                        </a>
                    </div>
                    <div class="col-auto">
                        <a href="{{ route('news.index') }}" class="btn btn-outline-light btn-lg px-4 rounded-pill">
                            <i class="fas fa-newspaper me-2"></i>{{ app()->getLocale() == 'fa' ? 'همه اخبار' : 'All News' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Stats Section -->
    <section class="stats-section py-5 bg-light">
        <div class="container">
            <div class="row g-4">
                <div class="col-6 col-lg-3">
                    <div class="p-4 bg-white rounded-lg shadow-soft text-center">
                        <h3 class="counter-value display-4 fw-bold text-turquoise" data-target="500">0</h3>
                        <p class="text-muted mb-0">{{ __('messages.satisfied_clients') }}</p>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="p-4 bg-white rounded-lg shadow-soft text-center">
                        <h3 class="counter-value display-4 fw-bold text-turquoise" data-target="15">0</h3>
                        <p class="text-muted mb-0">{{ __('messages.years_experience') }}</p>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="p-4 bg-white rounded-lg shadow-soft text-center">
                        <h3 class="counter-value display-4 fw-bold text-turquoise" data-target="100">0</h3>
                        <p class="text-muted mb-0">{{ __('messages.expert_team') }}</p>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="p-4 bg-white rounded-lg shadow-soft text-center">
                        <h3 class="counter-value display-4 fw-bold text-turquoise" data-target="1000">0</h3>
                        <p class="text-muted mb-0">{{ __('messages.completed_projects') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">{{ __('messages.our_services') }}</h2>
                <p class="lead text-muted">{{ __('messages.services_subtitle') }}</p>
            </div>
            <div class="row g-4">
                <!-- Service 1: Business & Commercial Services -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="100">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.business_commercial_services') }}</h4>
                            <p class="card-text">{{ __('messages.business_commercial_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service 2: Accounting, Auditing, Financial & Tax Services -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="200">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-file-invoice-dollar fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.financial_commercial_services') }}</h4>
                            <p class="card-text">{{ __('messages.financial_commercial_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service 3: Domestic & International Transport -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="300">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-truck-moving fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.domestic_international_transport') }}</h4>
                            <p class="card-text">{{ __('messages.transport_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service 4: Investment & Capital Funding -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="400">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.investment_capital') }}</h4>
                            <p class="card-text">{{ __('messages.investment_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service 5: Human Resource Supply -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="500">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.hr_supply') }}</h4>
                            <p class="card-text">{{ __('messages.hr_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service 6: Insurance Services -->
                <div class="col-12 col-sm-6 col-lg-4" data-animate="fade-up" data-animate-delay="600">
                    <div class="card service-card h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mb-4">
                                <i class="fas fa-shield-alt fa-2x"></i>
                            </div>
                            <h4 class="card-title mb-3">{{ __('messages.financial_reporting') }}</h4>
                            <p class="card-text">{{ __('messages.reporting_description') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Clients Section -->
    <section class="clients-section py-5 position-relative overflow-hidden">
        <!-- Background Elements -->
        <div class="position-absolute w-100 h-100 top-0 start-0"
             style="background: linear-gradient(135deg, rgba(0,0,128,0.08) 0%, rgba(64,224,208,0.15) 100%);
                    z-index: -2;">
        </div>
        <div class="position-absolute w-100 h-100 top-0 start-0"
             style="background-image: radial-gradient(rgba(64,224,208,0.2) 1.5px, transparent 1.5px);
                    background-size: 25px 25px;
                    opacity: 0.8;
                    z-index: -1;">
        </div>

        <div class="container">
            <div class="text-center mb-5">
                <span class="badge bg-turquoise-light text-turquoise px-3 py-2 mb-3">{{ __('messages.our_clients') }}</span>
                <h2 class="display-6 fw-bold mb-3">{{ __('messages.trusted_by_leaders') }}</h2>
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <p class="text-muted lead">{{ __('messages.empowering_sectors') }}</p>
                    </div>
                </div>
            </div>

            <div class="client-carousel">
                <div class="row g-4">
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="client-card">
                            <div class="card h-100 border-0 shadow-hover p-4">
                                <div class="client-logo mb-4">
                                    <img src="https://ui-avatars.com/api/?name=Tech+Solutions&background=40E0D0&color=fff&size=128&bold=true&format=svg"
                                         alt="{{ __('messages.tech_solutions') }}"
                                         class="img-fluid"
                                         style="max-height: 60px;">
                                </div>
                                <h3 class="h5 mb-3">{{ __('messages.tech_solutions') }}</h3>
                                <p class="text-muted mb-4">"{{ __('messages.tech_solutions_testimonial') }}"</p>
                                <div class="client-info d-flex align-items-center">
                                    <img src="https://i.pravatar.cc/150?img=1"
                                         alt="John Doe"
                                         class="rounded-circle me-3"
                                         width="40"
                                         height="40">
                                    <div>
                                        <h4 class="h6 mb-1">John Doe</h4>
                                        <p class="small text-muted mb-0">{{ __('messages.cfo') }}, {{ __('messages.tech_solutions') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="client-card">
                            <div class="card h-100 border-0 shadow-hover p-4">
                                <div class="client-logo mb-4">
                                    <img src="https://ui-avatars.com/api/?name=Global+MFG&background=40E0D0&color=fff&size=128&bold=true&format=svg"
                                         alt="{{ __('messages.global_manufacturing') }}"
                                         class="img-fluid"
                                         style="max-height: 60px;">
                                </div>
                                <h3 class="h5 mb-3">{{ __('messages.global_manufacturing') }}</h3>
                                <p class="text-muted mb-4">"{{ __('messages.global_manufacturing_testimonial') }}"</p>
                                <div class="client-info d-flex align-items-center">
                                    <img src="https://i.pravatar.cc/150?img=2"
                                         alt="Sarah Smith"
                                         class="rounded-circle me-3"
                                         width="40"
                                         height="40">
                                    <div>
                                        <h4 class="h6 mb-1">Sarah Smith</h4>
                                        <p class="small text-muted mb-0">{{ __('messages.financial_director') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="client-card">
                            <div class="card h-100 border-0 shadow-hover p-4">
                                <div class="client-logo mb-4">
                                    <img src="https://ui-avatars.com/api/?name=Retail+Dynamics&background=40E0D0&color=fff&size=128&bold=true&format=svg"
                                         alt="{{ __('messages.retail_dynamics') }}"
                                         class="img-fluid"
                                         style="max-height: 60px;">
                                </div>
                                <h3 class="h5 mb-3">{{ __('messages.retail_dynamics') }}</h3>
                                <p class="text-muted mb-4">"{{ __('messages.retail_dynamics_testimonial') }}"</p>
                                <div class="client-info d-flex align-items-center">
                                    <img src="https://i.pravatar.cc/150?img=3"
                                         alt="Mike Johnson"
                                         class="rounded-circle me-3"
                                         width="40"
                                         height="40">
                                    <div>
                                        <h4 class="h6 mb-1">Mike Johnson</h4>
                                        <p class="small text-muted mb-0">{{ __('messages.ceo') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="client-card">
                            <div class="card h-100 border-0 shadow-hover p-4">
                                <div class="client-logo mb-4">
                                    <img src="https://ui-avatars.com/api/?name=Healthcare+Plus&background=40E0D0&color=fff&size=128&bold=true&format=svg"
                                         alt="{{ __('messages.healthcare_plus') }}"
                                         class="img-fluid"
                                         style="max-height: 60px;">
                                </div>
                                <h3 class="h5 mb-3">{{ __('messages.healthcare_plus') }}</h3>
                                <p class="text-muted mb-4">"{{ __('messages.healthcare_plus_testimonial') }}"</p>
                                <div class="client-info d-flex align-items-center">
                                    <img src="https://i.pravatar.cc/150?img=4"
                                         alt="Emma Wilson"
                                         class="rounded-circle me-3"
                                         width="40"
                                         height="40">
                                    <div>
                                        <h4 class="h6 mb-1">Emma Wilson</h4>
                                        <p class="small text-muted mb-0">{{ __('messages.finance_manager') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <a href="{{ route('case-studies') }}" class="btn btn-outline-turquoise btn-lg">
                    {{ __('messages.view_success_stories') }}
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg">
                        <div class="card-body p-5">
                            <div class="text-center mb-5">
                                <span class="badge bg-turquoise-light text-turquoise px-4 py-2 mb-3" style="font-size: 1.1rem; color: #006666 !important;">{{ __('messages.contact_us') }}</span>
                                <h2 class="h1 mb-3">{{ __('messages.start_conversation') }}</h2>
                                <p class="text-muted">{{ __('messages.help_questions') }}</p>
                            </div>

                            <!-- Contact Form -->
                            <form action="/contact" method="POST" class="needs-validation" novalidate>
                                @csrf
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="name" name="name" placeholder="{{ __('messages.your_name') }}" required>
                                            <label for="name">{{ __('messages.your_name') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="email" name="email" placeholder="{{ __('messages.your_email') }}" required>
                                            <label for="email">{{ __('messages.your_email') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="{{ __('messages.your_phone') }}">
                                            <label for="phone">{{ __('messages.your_phone') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="service" name="service" required>
                                                <option value="">{{ __('messages.select_service') }}</option>
                                                <option value="business">{{ __('messages.business_commercial_services') }}</option>
                                                <option value="financial">{{ __('messages.financial_commercial_services') }}</option>
                                                <option value="transport">{{ __('messages.domestic_international_transport') }}</option>
                                                <option value="investment">{{ __('messages.investment_capital') }}</option>
                                                <option value="hr">{{ __('messages.hr_supply') }}</option>
                                                <option value="software">{{ __('messages.financial_reporting') }}</option>
                                            </select>
                                            <label for="service">{{ __('messages.service_interest') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <textarea class="form-control" id="message" name="message" placeholder="{{ __('messages.your_message') }}" style="height: 150px" required></textarea>
                                            <label for="message">{{ __('messages.your_message') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-12 text-center">
                                        <button type="submit" class="btn btn-turquoise btn-lg px-5">
                                            {{ __('messages.send_message') }}
                                            <i class="fas fa-paper-plane ms-2"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    <!-- CountUp.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/countup.js/2.0.8/countUp.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const options = {
                duration: 2.5,
                useEasing: true,
                useGrouping: true,
                separator: ",",
                decimal: "."
            };

            // Intersection Observer for triggering counters when visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const targetNum = parseInt(target.getAttribute('data-target'));
                        const countUp = new CountUp(target, targetNum, options);

                        if (!countUp.error) {
                            countUp.start();
                        } else {
                            console.error(countUp.error);
                        }

                        // Unobserve after starting animation
                        observer.unobserve(target);
                    }
                });
            }, {
                threshold: 0.1
            });

            // Observe all counter elements
            document.querySelectorAll('.counter-value').forEach(counter => {
                observer.observe(counter);
            });
        });
    </script>
@endsection
