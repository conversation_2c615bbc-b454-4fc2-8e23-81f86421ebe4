@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>{{ app()->getLocale() == 'fa' ? 'نمایش خبر' : 'View News' }}</h4>
                    <div>
                        <a href="{{ route('admin.news.edit', $news) }}" class="btn btn-warning">
                            {{ app()->getLocale() == 'fa' ? 'ویرایش' : 'Edit' }}
                        </a>
                        <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                            {{ app()->getLocale() == 'fa' ? 'بازگشت' : 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'عنوان:' : 'Title:' }}</strong>
                        <p>{{ $news->title }}</p>
                    </div>

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'نامک:' : 'Slug:' }}</strong>
                        <p>{{ $news->slug }}</p>
                    </div>

                    @if($news->excerpt)
                        <div class="mb-3">
                            <strong>{{ app()->getLocale() == 'fa' ? 'خلاصه:' : 'Excerpt:' }}</strong>
                            <p>{{ $news->excerpt }}</p>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'محتوا:' : 'Content:' }}</strong>
                        <div class="border p-3 bg-light">
                            {!! nl2br(e($news->content)) !!}
                        </div>
                    </div>

                    @if($news->category)
                        <div class="mb-3">
                            <strong>{{ app()->getLocale() == 'fa' ? 'دسته‌بندی:' : 'Category:' }}</strong>
                            <p>{{ $news->category->name }}</p>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'وضعیت:' : 'Status:' }}</strong>
                        <span class="badge bg-{{ $news->is_active ? 'success' : 'secondary' }}">
                            {{ $news->is_active ? (app()->getLocale() == 'fa' ? 'فعال' : 'Active') : (app()->getLocale() == 'fa' ? 'غیرفعال' : 'Inactive') }}
                        </span>
                    </div>

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'ویژه:' : 'Featured:' }}</strong>
                        <span class="badge bg-{{ $news->is_featured ? 'primary' : 'secondary' }}">
                            {{ $news->is_featured ? (app()->getLocale() == 'fa' ? 'بله' : 'Yes') : (app()->getLocale() == 'fa' ? 'خیر' : 'No') }}
                        </span>
                    </div>

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'تاریخ ایجاد:' : 'Created At:' }}</strong>
                        <p>{{ $news->created_at->format('Y-m-d H:i:s') }}</p>
                    </div>

                    <div class="mb-3">
                        <strong>{{ app()->getLocale() == 'fa' ? 'آخرین به‌روزرسانی:' : 'Updated At:' }}</strong>
                        <p>{{ $news->updated_at->format('Y-m-d H:i:s') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
