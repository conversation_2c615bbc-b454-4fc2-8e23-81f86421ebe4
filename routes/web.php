<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ArticleController as AdminArticleController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/services', function () {
    return view('services');
})->name('services');

// Service Pages Routes
Route::get('/services/business', function () {
    return view('services.business');
})->name('services.business');

Route::get('/services/financial', function () {
    return view('services.financial');
})->name('services.financial');

Route::get('/services/transport', function () {
    return view('services.transport');
})->name('services.transport');

Route::get('/services/investment', function () {
    return view('services.investment');
})->name('services.investment');

Route::get('/services/hr', function () {
    return view('services.hr');
})->name('services.hr');

Route::get('/services/insurance', function () {
    return view('services.insurance');
})->name('services.insurance');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// اضافه کردن مسیر جدید برای case studies
Route::get('/case-studies', function () {
    return view('case-studies');
})->name('case-studies');

Route::get('language/{locale}', function ($locale) {
    if (in_array($locale, config('app.available_locales'))) {
        app()->setLocale($locale);
        session()->put('locale', $locale);
    } else {
        // If invalid locale, use default
        app()->setLocale(config('app.locale'));
        session()->put('locale', config('app.locale'));
    }
    return redirect()->back();
})->name('language.switch');

// مسیرهای عمومی مقالات و اخبار
Route::prefix('articles')->name('articles.')->group(function () {
    Route::get('/', [ArticleController::class, 'index'])->name('index');
    Route::get('/category/{slug}', [ArticleController::class, 'category'])->name('category');
    Route::get('/{slug}', [ArticleController::class, 'show'])->name('show');
});

Route::prefix('news')->name('news.')->group(function () {
    Route::get('/', [NewsController::class, 'index'])->name('index');
    Route::get('/category/{slug}', [NewsController::class, 'category'])->name('category');
    Route::get('/{slug}', [NewsController::class, 'show'])->name('show');
});

// مسیرهای پنل مدیریت
Route::middleware(['admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // مدیریت دسته‌بندی‌ها
    Route::resource('categories', AdminCategoryController::class);

    // مدیریت مقالات
    Route::resource('articles', AdminArticleController::class);
    Route::post('articles/{article}/toggle-featured', [AdminArticleController::class, 'toggleFeatured'])->name('articles.toggle-featured');

    // مدیریت اخبار
    Route::resource('news', AdminNewsController::class);
    Route::post('news/{news}/toggle-featured', [AdminNewsController::class, 'toggleFeatured'])->name('news.toggle-featured');
});

Auth::routes();

// Redirect /home to admin panel
Route::get('/home', function () {
    return redirect()->route('admin.dashboard');
})->middleware('auth')->name('home');
