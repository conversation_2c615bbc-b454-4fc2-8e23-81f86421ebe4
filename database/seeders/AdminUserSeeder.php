<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // بررسی اینکه آیا کاربر مدیر قبلاً وجود دارد یا نه
        $adminUser = User::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            // ایجاد کاربر مدیر جدید
            User::create([
                'name' => 'مدیر سیستم',
                'email' => '<EMAIL>',
                'password' => Hash::make('lian7654321'),
                'email_verified_at' => now(),
            ]);

            echo "کاربر مدیر با موفقیت ایجاد شد.\n";
            echo "ایمیل: <EMAIL>\n";
            echo "رمز عبور: lian7654321\n";
        } else {
            // به‌روزرسانی رمز عبور کاربر موجود
            $adminUser->update([
                'password' => Hash::make('lian7654321'),
            ]);

            echo "رمز عبور کاربر مدیر به‌روزرسانی شد.\n";
            echo "ایمیل: <EMAIL>\n";
            echo "رمز عبور جدید: lian7654321\n";
        }
    }
}
