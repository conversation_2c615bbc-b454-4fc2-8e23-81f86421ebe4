<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Article;
use App\Models\News;
use App\Models\Category;

class ContentCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // ارتباط مقالات با دسته‌بندی‌ها
        $this->attachArticlesToCategories();
        
        // ارتباط اخبار با دسته‌بندی‌ها
        $this->attachNewsToCategories();
        
        echo "محتوا با موفقیت به دسته‌بندی‌ها متصل شد.\n";
    }

    private function attachArticlesToCategories()
    {
        // دریافت مقالات
        $articles = Article::all();
        
        // دریافت دسته‌بندی‌های مقالات
        $accountingCategory = Category::where('slug', 'accounting')->first();
        $taxCategory = Category::where('slug', 'tax')->first();
        $businessCategory = Category::where('slug', 'business')->first();
        $generalCategory = Category::where('slug', 'general')->first();

        foreach ($articles as $article) {
            // بر اساس slug مقاله، دسته‌بندی مناسب را انتخاب می‌کنیم
            $categories = [];
            
            if (str_contains($article->slug, 'financial') || str_contains($article->slug, 'accounting')) {
                if ($accountingCategory) $categories[] = $accountingCategory->id;
            }
            
            if (str_contains($article->slug, 'audit')) {
                if ($accountingCategory) $categories[] = $accountingCategory->id;
            }
            
            if (str_contains($article->slug, 'business') || str_contains($article->slug, 'growth')) {
                if ($businessCategory) $categories[] = $businessCategory->id;
            }
            
            if (str_contains($article->slug, 'inflation') || str_contains($article->slug, 'management')) {
                if ($businessCategory) $categories[] = $businessCategory->id;
            }
            
            // اگر هیچ دسته‌بندی خاصی پیدا نشد، به دسته عمومی اضافه کن
            if (empty($categories) && $generalCategory) {
                $categories[] = $generalCategory->id;
            }
            
            // اتصال مقاله به دسته‌بندی‌ها
            if (!empty($categories)) {
                $article->categories()->sync($categories);
                echo "مقاله '{$article->title_fa}' به دسته‌بندی‌ها متصل شد.\n";
            }
        }
    }

    private function attachNewsToCategories()
    {
        // دریافت اخبار
        $news = News::all();
        
        // دریافت دسته‌بندی‌های اخبار
        $taxNewsCategory = Category::where('slug', 'tax-news')->first();
        $economicNewsCategory = Category::where('slug', 'economic-news')->first();
        $generalCategory = Category::where('slug', 'general')->first();

        foreach ($news as $newsItem) {
            // بر اساس slug خبر، دسته‌بندی مناسب را انتخاب می‌کنیم
            $categories = [];
            
            if (str_contains($newsItem->slug, 'tax')) {
                if ($taxNewsCategory) $categories[] = $taxNewsCategory->id;
            }
            
            if (str_contains($newsItem->slug, 'accounting') || str_contains($newsItem->slug, 'business')) {
                if ($economicNewsCategory) $categories[] = $economicNewsCategory->id;
            }
            
            if (str_contains($newsItem->slug, 'technology') || str_contains($newsItem->slug, 'innovation')) {
                if ($economicNewsCategory) $categories[] = $economicNewsCategory->id;
            }
            
            // اگر هیچ دسته‌بندی خاصی پیدا نشد، به دسته عمومی اضافه کن
            if (empty($categories) && $generalCategory) {
                $categories[] = $generalCategory->id;
            }
            
            // اتصال خبر به دسته‌بندی‌ها
            if (!empty($categories)) {
                $newsItem->categories()->sync($categories);
                echo "خبر '{$newsItem->title_fa}' به دسته‌بندی‌ها متصل شد.\n";
            }
        }
    }
}
